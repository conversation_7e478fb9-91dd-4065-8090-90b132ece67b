import {
  Controller,
  Get,
  Query,
} from '@nestjs/common';
import { DerCronService } from "../services";
import { SchedulerRegistry } from "@nestjs/schedule";
@Controller('cron')
export class CronController {
  constructor(
    private readonly derService :DerCronService ,
    private schedulerRegistry: SchedulerRegistry
  ) { }
  @Get('run')
  async getListSyncThirdparty (
    @Query('datetime')datetime: string
  ): Promise<any>{
    return await this.derService.getListSyncThirdparty(datetime)
  }
}
