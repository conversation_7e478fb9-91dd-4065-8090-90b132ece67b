PORT=6789
ENV_NODE=development #development # production #staging

# API FOS 
FOS_URL_BASE=http://***********:7684/apigw
# FOS_URL_BASE=http://***********:7684/APICall

#ZeroMQ
API_KEY=CHANGEME
REQ_URL=tcp://***********:6868
REQ_URL_HOS=tcp://***********:5555
PULL_URL=tcp://***********:2706

# Thời gian fetch dữ liệu mới lowdb
TIME_RESET_CACHE=5000

#Cron
TIME_CRON_UPDATE_DER=0 * * * * * #0 */1 * * *
TYPE_UPDATE_DER=TYPE_UPDATE_DER
#Retry call API Axios
AXIOS_RETRIES_NUM=3

# Redis - header
HEADER_GET_STK_MKT=85680a1aeb82a95c561838e9bb93102872fdbdc59a9b4e69d162c0dd1e241fb9
REDIS_HOST=***********
REDIS_PORT=6379

# Phái sinh
PS_HOSTNAME=http://************:8100
PS_API_KEY=cFIYnlsxw0I4iXdZoZvVvXpmdAdgGdA3

