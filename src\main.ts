import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger, LoggerModuleService } from './common/logging';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import * as dotenv from 'dotenv';
import * as path from 'path';
import { WssAdapter } from './adapters/wss-adapter';

import { rateLimit } from 'express-rate-limit';
import { GlobalService } from "./utils";
const requestIp = require('request-ip');

export const globalRequestLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50000, // limit each IP to 50000 requests per windowMs
  message: 'Too many requests from this IP, please try again later',
  keyGenerator: (req) => requestIp.getClientIp(req),
  handler: function (req, res) {

    const clientIp = requestIp.getClientIp(req);
    console.log('Too many requests from this IP ', clientIp)

    res.status(429).send({
      status: 500,
      message: 'Too many requests from this IP, please try again later!',
    });
  }
});

dotenv.config({
  path: path.join(process.cwd(), './.env'),
});

console.log('path ' + path.join(process.cwd(), './.env'));

async function bootstrap() {
  const app = await NestFactory.create(AppModule, Logger());
  const logger = new LoggerModuleService('global')

  app.getHttpAdapter().getInstance().disable('x-powered-by');
  app.use(globalRequestLimit);

  app.useWebSocketAdapter(new WssAdapter(app));
  app.useGlobalPipes(new ValidationPipe({ transform: true }));

  const configSwagger = new DocumentBuilder()
    .setTitle('API-SSV')
    .setDescription('The system designed by SSV-DS team ')
    .setVersion('1.0')
    .addTag('SSV-DS')
    .addBearerAuth(
      {
        description: 'Default JWT Authorization',
        bearerFormat: 'JWT',
        scheme: 'Bearer',
        type: 'http', // I`ve attempted type: 'apiKey' too
        in: 'Header',
      },
      'APIKEY',
    )
    .build();
  if (process.env.ENV_NODE != 'production') {
    const document = SwaggerModule.createDocument(app, configSwagger);
    SwaggerModule.setup('api', app, document);
  }
  const port = process.env.PORT || 9696;
  const host = process.env.HOST || 'localhost';

  await app.listen(port);

  logger.logInfo(
    `Application listening on ${process.env.PORT} (ENV: ${process.env.ENV_NODE}) : http://${host}:${port}`,
  );
}
bootstrap();
