import { Injectable } from '@nestjs/common';
import { Subject } from 'rxjs';

import { ConfigService } from '@nestjs/config';
// import {
//   ServerRequestApi,
//   ServerResponseApi,
// } from '../api-infobip/dto/ServerAPI.dto';
import { ZeroMq4Connection } from './ZeroMqConnection';
import { ServerRequestApi, ServerResponseApi } from '../open-api/dto';

export interface ZeroMqSocket {
  apiKey: string;
  reqUrl: string;
  reqHosUrl: string;
  pullUrl: string;
}

// Số 1
@Injectable()
export class ZeroMqService {
  private socketZeroMq = this.configService.get<ZeroMqSocket>('ZeroMqSocket');

  private ZeroMq = new ZeroMq4Connection({
    reqUrl: this.socketZeroMq.reqUrl,
    pullUrl: this.socketZeroMq.pullUrl,
    reqHosUrl: this.socketZeroMq.reqHosUrl,
  });

  messageSubject: Subject<any> = new Subject<unknown>();
  constructor(private configService: ConfigService) { }
  // Được khởi tạo từ gateway.ts
  connect() {
    this.ZeroMq.connect();
  }
  // Được dùng để gọi Data từ FOS
  public async SendToFOSBackend(data: ServerRequestApi): Promise<ServerResponseApi> {
    return await this.ZeroMq.SSVMWCreateConnection(data);
  }

  // Được dùng để gọi Data từ HOS
  public async SendToHOSBackend(
    data: ServerRequestApi,
  ): Promise<ServerResponseApi> {
    console.log("this.ZeroMq.SSVMWHosCreateConnection call!");

    return await this.ZeroMq.SSVMWHosCreateConnection(data);
  }
}
