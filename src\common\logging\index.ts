import { LoggerService } from '@nestjs/common';
import { WinstonModule } from 'nest-winston';
const winston = require('winston');
const moment = require('moment');

// Quản lý log và xuất logger theo file mỗi ngày => server sẽ restart vào mỗi 3h sáng
export const Logger = () => {
  winston.addColors({
    error: 'red',
    warn: 'yellow',
    info: 'green',
    debug: 'blue',
  });
  const datetime = moment().format('YYYYMMDD')
  winston.format.combine(winston.format.colorize(), winston.format.simple());
  const appOptions = {
    cors: true,
    logger: WinstonModule.createLogger({
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.timestamp({
              format: 'YYYY-MM-DD HH:mm:ss:SSS',
            }),
            winston.format.printf((info) => {
              const { timestamp, level, message, context, ...args } = info;
              return `[${level}]-[${context}] ${timestamp}:  ${message} \n`;
            }),
          ),
        }), // ==> 1
        new winston.transports.File({
          filename:`logs/${datetime}/global/info.log`,
          level: 'info',
          handleExceptions: true,
        }),
        new winston.transports.File({
          filename:`logs/${datetime}/global/error.log`,
          level: 'error',
          handleExceptions: true,
        }),
        new winston.transports.File({          
          filename:`logs/${datetime}/global/debug.log`,
          level: 'debug',
          handleExceptions: true,
        }),
      ],

      format: winston.format.combine(
        winston.format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss:SSS',
        }),
        winston.format.printf(
          (error) =>
            `${error.level.toUpperCase()}-[${error.context}]: ${[
              error.timestamp,
            ]}: ${error.message}`,
        ),
      ),
    }),
  };
  return appOptions;
};

export const LoggerModule = (contextName?:string) => { 
  const datetime = moment().format('YYYYMMDD');
  
  winston.addColors({
    error: 'red',
    warn: 'yellow',
    info: 'green',
    debug: 'blue',
  });
  
  return WinstonModule.createLogger({
    transports: [
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.timestamp({
            format: 'YYYY-MM-DD HH:mm:ss:SSS',
          }),
          winston.format.printf((info) => {
            const { timestamp, level, message, context, ...args } = info;
            return `[${level}]-[${context}] ${timestamp}:  ${message} \n`;
          }),
        ),
      }), 
      new winston.transports.File({
        filename:`logs/${datetime}/info/${contextName}.log`,
        level: 'info',
        handleExceptions: true,
      }),
      new winston.transports.File({
        filename:`logs/${datetime}/error/${contextName}.log`,
        level: 'error',
        handleExceptions: true,
      })   
    ],
    format: winston.format.combine(
      winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss:SSS',
      }),
      winston.format.printf(
        (error) => `${error.level.toUpperCase()}-[${error.context}]: ${[error.timestamp]}: ${error.message}`,
      ),
    ),
  })
}
/// bổ sung Config log
export interface ILoggerModuleService {
  logError: ( message1: any, message2?: any)  => void
  logInfo:  ( message1: any, message2?: any)  => void
  logDebug: ( message1: any, message2?: any)  => void
}
export class LoggerModuleService implements ILoggerModuleService{
  private logger: LoggerService;
  private serviceName : any

  constructor(serviceName:any){
    this.serviceName = serviceName
    this.logger = LoggerModule(serviceName)
  }

  LoggerModuleService(serviceName:any){
    this.serviceName = serviceName
  }

  logInfo(message1:any, message2?:any){
    this.logger.log((message1 ??'') + (message2 ??''),[this.serviceName])
  }
  logError(message1:any, message2?:any){
    this.logger.error((message1 ??'') + (message2 ??''),[this.serviceName])
  }
  logDebug(message1: any, message2?: any) {
    this.logger.debug((message1 ??'') + (message2 ??''),[this.serviceName])
  }
  logWarn(message1: any, message2?: any) {
    this.logger.warn((message1 ??'') + (message2 ??''),[this.serviceName])
  }
}