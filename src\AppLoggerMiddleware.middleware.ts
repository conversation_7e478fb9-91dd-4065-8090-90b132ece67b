import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { LoggerModuleService } from './common';

@Injectable()
export class AppLoggerMiddleware implements NestMiddleware {
    private logger = new LoggerModuleService('AppLoggerMiddleware')
    constructor(){
        this.logger = new LoggerModuleService('AppLoggerMiddleware')
    }
    use(request: Request, response: Response, next: NextFunction): void {
        const { ip, method, path: url } = request;
        const getime =(new Date()).getTime()
        response.on('close', () => {
            const { statusCode, statusMessage } = response;
            this.logger.logInfo( `Logging HTTP request ${method} ${url} ${statusCode}${ip} || ` + JSON.stringify({
                req:{
                    id: getime,
                    headers: request.headers,
                    body: request.body,
                    query: request.query,
                    params: request.params,
                    url : request.url,
                    path : request.path,
                    method : request.method,
                    rateLimit : request['rateLimit'],
                },
                res:{
                    id: getime,
                    statusCode: statusCode,
                    statusMessage: statusMessage,
                }
            }))
        });
        next();
    }
}