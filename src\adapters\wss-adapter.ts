import * as WebSocket from 'ws';
import { WebSocketAdapter, INestApplicationContext } from '@nestjs/common';
import { MessageMappingProperties } from '@nestjs/websockets';
import { Observable, fromEvent, EMPTY } from 'rxjs';
import { mergeMap, filter } from 'rxjs/operators';
import * as https from 'https';
import { readFileSync } from 'fs';

export class WssAdapter implements WebSocketAdapter {
  constructor(private app: INestApplicationContext) { }

  create(port: number, options: any = {}): any {
    if (process.env.SSL) {
      const server = https
        .createServer({
          cert: readFileSync('/app/certs/ssl-bundle.pem'),
          key: readFileSync('/app/certs/ssl-private.pem'),
        })
        .listen(port);
      return new WebSocket.Server({ server, ...options });
    } else {
      return new WebSocket.Server({ port, ...options });
    }
  }

  bindClientConnect(server, callback: Function) {
    server.on('connection', callback);
  }

  bindMessageHandlers(
    client: WebSocket,
    handlers: MessageMappingProperties[],
    process: (data: any) => Observable<any>,
  ) {
    fromEvent(client, 'message')
      .pipe(
        mergeMap((data) => this.bindMessageHandler(data, handlers, process)),
        filter((result) => result),
      )
      .subscribe((response) => client.send(JSON.stringify(response)));
  }

  bindMessageHandler(
    buffer,
    handlers: MessageMappingProperties[],
    process: (data: any) => Observable<any>,
  ): Observable<any> {
    const message = JSON.parse(buffer.data);
    const messageHandler = handlers.find(
      (handler) => handler.message === message.event,
    );
    if (!messageHandler) {
      return EMPTY;
    }
    return process(messageHandler.callback(message.data));
  }

  close(server) {
    server.close();
  }
}
