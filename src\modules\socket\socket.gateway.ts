import {
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
  OnGatewayInit,
  WsResponse,
  MessageBody,
} from '@nestjs/websockets';
import { from, Observable, Subscription } from 'rxjs';
import { Server } from 'ws';
import { ZeroMqService } from '../zeromq/zeromq.service';

@WebSocketGateway(8888)
export class SocketGateway implements OnGatewayInit {
  @WebSocketServer()
  server: Server;
  subscription: Subscription;
  currentRates: any;

  constructor(private mtService: ZeroMqService) {
    this.mtService.connect();
  }

  afterInit(socketServer) {
    socketServer.on('connection', (ws: WebSocket) => {
      this.subscription = this.mtService.messageSubject.subscribe((data) => {
        console.log('data->', data);
      });
    });
  }
}
