import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ServerRequestApi, ServerResponseApi } from 'src/modules/open-api/dto';
import axios from 'axios';
import { ZeroMqService } from 'src/modules/zeromq/zeromq.service';
import { GlobalService } from 'src/utils/global.service';
const chalk = require('chalk');

@Injectable()
export class ServerAPIService {
  constructor(
    private configService: ConfigService,
    private zeroMqService: ZeroMqService,
  ) { }
  private readonly logger = new Logger(ServerAPIService.name);

  // API call hệ thống FOS use line API  FOSGATE ALPHA
  async callApiFos(
    data: ServerRequestApi,
    callback?: Function,
  ): Promise<ServerResponseApi> {
    let result = new ServerResponseApi();
    this.logger.log(
      '>>>>>>>> START ' + data.ServiceName + ' | ' + JSON.stringify(data),
    );
    try {
      let config = {
        method: 'POST',
        baseURL: this.configService.get<string>('FOS_URL_BASE'),
        headers: {
          //   'Authorization': 'App ' + this.configService.get<string>('BIP_URL_AUTH_APP'),
          'Content-Type': 'application/json',
        },
        data: JSON.stringify(data),
        rejectUnauthorized: false,
        timeout: data.TimeOut * 1000 || 15000,
      };
      const resApi = await axios.request(config);
      this.logger.log(
        '>>>>>>>> END ' +
        data.ServiceName +
        ' | ' +
        JSON.stringify(resApi.data),
      );
      if (resApi.status == 200) {
        try {
          result.data = JSON.parse(resApi.data['Content']['Data']) || '';
        } catch (error) {
          result.data = resApi.data['Content']['Data'];
        }
        result.msgCd = resApi.data['Content']['Code'];
        result.message = resApi.data['Content']['Message'];
        result.success = resApi.data['Content']['Result'] == '1' ? true : false;
        return result;
      }
      return result;
    } catch (error) {
      this.logger.log(
        '>>>>>>>> END ERROR ' +
        data.ServiceName +
        ' | ' +
        JSON.stringify(error),
      );
      return result;
    } finally {
      this.logger.log(JSON.stringify(result));
    }
  }

  // API call hệ thống FOS use line Socket
  async callSocketFos(data: ServerRequestApi): Promise<ServerResponseApi> {
    try {
      this.logger.log(
        '>>>>>>>> START  ' + data.ServiceName + ' | ' + JSON.stringify(data),
      );
      return this.zeroMqService.SendToFOSBackend(data);
    } catch (error) {
      this.logger.log(
        '>>>>>>>> END ERROR ' +
        data.ServiceName +
        ' | ' +
        JSON.stringify(error),
      );
    } finally {
      this.logger.log(
        '>>>>>>>> END  ' + data.ServiceName + ' | ' + JSON.stringify(data),
      );
    }
  }

  // API call hệ thống HOS use line Socket
  async callSocketHos(data: ServerRequestApi): Promise<ServerResponseApi> {
    try {
      this.logger.log(
        '>>>>>>>> START  ' + data.ServiceName + ' | ' + JSON.stringify(data),
      );
      //waiting zeromq connect
      GlobalService.flagSuccessConnect = false;

      console.log(
        chalk.yellow(
          ' GlobalService.flagSuccessConnect set before connect>>>' +
          GlobalService.flagSuccessConnect,
        ),
      );

      let result = await this.zeroMqService.SendToHOSBackend(data);
      //console.log(chalk.green('result call return: ' + JSON.stringify(result)));

      GlobalService.flagSuccessConnect = true;

      return result;
    } catch (error) {
      console.log(
        chalk.red(
          '>>>>>>>>>>>>>>>>>>>>>>>>>>>>ERROR: ' + JSON.stringify(error),
        ),
      );
      this.logger.log(
        '>>>>>>>> END ERROR ' +
        data.ServiceName +
        ' | ' +
        JSON.stringify(error),
      );
    } finally {
      this.logger.log(
        '>>>>>>>> END  ' + data.ServiceName + ' | ' + JSON.stringify(data),
      );
      GlobalService.flagSuccessConnect = true;
      console.log(
        chalk.yellow(
          ' GlobalService.flagSuccessConnect set finally connect >>>>' +
          GlobalService.flagSuccessConnect,
        ),
      );
    }
  }
}
