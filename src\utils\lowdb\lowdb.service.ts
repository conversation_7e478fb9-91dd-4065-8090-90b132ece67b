import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import lowdb from 'lowdb';
import FileAsync from 'lowdb/adapters/FileAsync';
import moment from 'moment';
import * as uuid from 'uuid';
import * as fs from 'fs';

enum TableDefault {
  accounts = 'accounts',
  hisUpdate = 'hisUpdate', // Thể hiện thời gian gần nhất table cần cập nhật mới.
}

@Injectable()
export class LowdbService implements ILowDbService {
  private db: lowdb.LowdbAsync<any>;
  constructor(private configService: ConfigService) {
    this.initTable(TableDefault.accounts);
    this.initTable(TableDefault.hisUpdate);
  }

  async initTable(table: string) {
    const env_node = this.configService.get<string>('ENV_NODE');
    const filename = env_node == 'production' ? 'db-prod.json' : 'db-uat.json';
    try {
      const adapter = new FileAsync(filename);
      this.db = await lowdb(adapter);

      const listAccount = await this.db.get(table).value();
      if (!listAccount) {
        await this.db.set(table, []).write();
      }
      await this.trackingHisUpt(table);
      return await this.getListTable();
    } catch (e) {
      console.log(e);
      if (fs.existsSync(filename)) {
        await fs.promises.unlink(filename);
      }

      return [];
    }
  }

  async getListTable(): Promise<any> {
    //PhuongNh force sync data
    await this.db.read();

    const _listTb = await this.db.getState();
    let list = {};
    Object.keys(_listTb).forEach(function (key) {
      list[key] = _listTb[key].length;
    });
    return list;
  }

  async findAll(table: string): Promise<any> {
    //PhuongNh force sync data
    await this.db.read();

    const listAccount = await this.db.get(table).value();
    return listAccount;
  }

  async find(condition: object, table: string): Promise<any> {
    //PhuongNh force sync data
    await this.db.read();

    const values = await this.db.get(table).find(condition).value();
    return values;
  }

  async update(
    key: string,
    value: string,
    table: string,
    dataUpdate: any,
  ): Promise<any> {
    const list = await this.db.get(table).value();
    let out;
    const newList = list.map((item) => {
      if (item[key] !== value) return item;
      if (item[key] === value) {
        out = Object.assign(item, dataUpdate);
        return out;
      }
    });
    await this.db.set(table, newList).write();
    return out;
  }

  async add(record: any, table: string): Promise<any> {
    try {
      const list = await this.db.get(table).value();
      record.id = uuid.v1();
      list.push(record);
      await this.db.set(table, list).write();
      return record;
    } catch (e) {
      console.log(e);
      return record;
    }
  }

  async addlist(record: any[], table: string, force?: boolean): Promise<any> {
    await this.trackingHisUpt(table);
    let list = [];
    if (!force) {
      list = (await this.db.get(table).value()) || [];
    }
    const listNew = list.concat(record).map((ele) => ({
      ...ele,
      id: uuid.v1(),
    }));
    await this.db.set(table, listNew).write();
    return await this.db.get(table).value();
  }

  async trackingHisUpt(table: string): Promise<any> {
    if (table != TableDefault.hisUpdate) {
      const arrRecord = (await this.findAll(TableDefault.hisUpdate)) || [];
      const record = arrRecord.find((item) => item[`${table}`]);
      if (record) {
        await this.update(table, record[`${table}`], TableDefault.hisUpdate, {
          ...record,
          [`${table}`]: moment().valueOf(),
        });
      } else {
        await this.add(
          { [`${table}`]: moment().valueOf() },
          TableDefault.hisUpdate,
        );
      }
      return true;
    }
    return false;
  }
}
