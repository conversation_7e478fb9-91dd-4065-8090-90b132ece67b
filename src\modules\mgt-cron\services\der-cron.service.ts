import {
  Injectable,
  Logger,
  OnModuleInit,
  OnModuleDestroy
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { SchedulerRegistry } from "@nestjs/schedule";
import { CronJob } from "cron";
import { ServerRequestApi } from "src/modules/open-api/dto";
import { GetIPAddress, GlobalService } from "../../../utils/global.service";

const moment = require("moment");
import { LowdbService } from "src/utils";
import {  ServerAPIService } from "src/modules/open-api/services";
import axios, { AxiosRequestConfig, AxiosResponse, CreateAxiosDefaults } from "axios";
import { ILoggerModuleService, LoggerModuleService } from "src/common";

const retry = require("promise-retry");
const _ = require("lodash");

enum TableDefault {
  accounts = "accounts",
}

@Injectable()
export class DerCronService implements OnModuleInit, OnModuleDestroy {
  private logger: ILoggerModuleService;
  constructor(
    private configService: ConfigService,
    private lowdbService: LowdbService,
    private schedulerRegistry: SchedulerRegistry,
    private server: ServerAPIService,
  ) {
    this.logger = new LoggerModuleService(DerCronService.name)
  }

  onModuleInit() {   
    const TYPE_CRON =
      this.configService.get<string>("TYPE_UPDATE_DER") || "TYPE_UPDATE_DER";
      this.logger.logInfo('TYPE_CRON: ', TYPE_CRON);

      if (TYPE_CRON == "TYPE_UPDATE_DER") {
      //Cron UPDATE STATUS DER
      const TIME_CRON_UPDATE_DER =
        this.configService.get<string>("TIME_CRON_UPDATE_DER") ||
        "0 * * * * *";

      this.logger.logInfo(
        "TIME_CRON_UPDATE_DER >> " + TIME_CRON_UPDATE_DER
      );

      const jobInsert = new CronJob(TIME_CRON_UPDATE_DER, () => {
        // What you want to do here
        this.logger.logInfo(
          moment().format("YYYY_MM_DD HH:MM:SS") +
          " <<*TIME_CRON_UPDATE_DER start*>> "
        );
        GlobalService.checkInsertQueue = true;

        this.getListSyncThirdparty();
      });

      this.schedulerRegistry.addCronJob(
        "TIME_CRON_UPDATE_DER",
        jobInsert
      );

      jobInsert.start();
    }

  }

  onModuleDestroy() {
    this.logger.logInfo(
      moment().format("YYYY_MM_DD HH:MM:SS") + " cronjob destroy"
    );
  }

  public async getListSyncThirdparty (input?:string): Promise<any> {
    try {
      const datetime = input ?? moment().format('YYYYMMDD')
      this.logger.logInfo('datetime: ', datetime);
      let svApiFmt = new ServerRequestApi();
      svApiFmt.ClientSeq = new Date().getTime();
      svApiFmt.Lang           = 'VI', 
      svApiFmt.WorkerName     = 'ALTqCommon02',           //'FOSqMkt02';
      svApiFmt.Operation      = 'Q',                      //'Q';
      svApiFmt.ServiceName    = 'ALTqCommon02_QuerySyncThirdParty',         //'FOSqMkt02_IndexMgt';
      svApiFmt.InVal          = [datetime];
      svApiFmt.TotInVal       = 1;
      svApiFmt.IPPrivate      = GetIPAddress();
      svApiFmt.IPPublic       = GetIPAddress();
      this.logger.logInfo('------------- Input GetListSyncThirdparty: ' + JSON.stringify(svApiFmt)); 
      // Thêm thông số không cần thiết nhưng phải khai báo
      const result = await this.server.callSocketHos(svApiFmt);
      this.logger.logInfo('-------------result < '+ JSON.stringify(result)+' >----------------')
      if(result?.success){
        // thực thi gọi axios
        const input = {
          acnt_no:'',
          ident_no:'',
          acnt_pwd:'',
          cust_nm:'',
          cust_eng_nm:'',
          ind_corp:'F',
          tax_yn:'2',
          sms_use_yn:'1',
          mobile:'',
          hts_id:'',
          brcd:'',
          agnc:'',
          tel_code:'',
        } 
        // gọi API get thông tin
        this.logger.logInfo('-------------Total Record < '+ JSON.stringify(result.data.length)+' >----------------')
        if(result?.data?.length > 0){
          for(let i = 0; i < result.data?.length; i++){
            this.logger.logInfo('-------------Detail Record < '+ JSON.stringify(result.data[i].c3)+' >----------------\n\n')
            svApiFmt.WorkerName     = 'ALTqDerivatives',
            svApiFmt.Operation      = 'Q',                     
            svApiFmt.ServiceName    = 'ALTqDerivatives_GetAccount',
            svApiFmt.InVal          = [result.data[i].c3];
            svApiFmt.TotInVal       = 1;
            const getDetail = await this.server.callSocketHos(svApiFmt);
            if(getDetail.success && getDetail.data.length){
              const detail:any = JSON.parse(getDetail.data)[0]
              // gọi API partner
              input.acnt_no     = detail.c0
              input.hts_id      = detail.c0
              input.cust_nm     = detail.c1
              input.cust_eng_nm = detail.c1
              input.ident_no    = detail.c2
              input.ind_corp    = detail.c6
              input.acnt_pwd    = detail.c4
              input.brcd        = detail.c7
              input.agnc        = detail.c8
              input.tax_yn      = detail.c10 ?? '2'
              input.mobile      = detail.c12
              input.sms_use_yn  = detail.c14 ?? '1'
              input.tel_code  = detail.c29 ?? ''
              this.logger.logInfo('-------------Detail Info < '+ JSON.stringify(detail)+' >----------------\n\n')
              this.logger.logInfo('-------------Detail Info: input < '+ JSON.stringify(input)+' >----------------\n\n')

              const rsSend = await this.axiosSendPartner(input);
              const rsSendChangePW = await this.axiosSendChangePW({account_no: detail.c0, password: detail.c29 });
              this.logger.logInfo('rsSendChangePW: ', JSON.stringify( rsSendChangePW.data));
              this.logger.logInfo('rsSendChangePW.status: ', rsSendChangePW.status);
              this.logger.logInfo('rsSend: ', JSON.stringify( rsSend.data));
              this.logger.logInfo('rsSend.status: ', rsSend.status);
              if(rsSend.status != 200){
                // gửi thông tin lỗi retry 3 lần lần 3s
                svApiFmt.InVal          = [datetime, result.data[i].c1 ,'9',rsSend.statusText];
              }else{
                // gọi API update.
                svApiFmt.WorkerName     = 'ALTxCommon02',           
                svApiFmt.Operation      = 'U',                     
                svApiFmt.ServiceName    = 'ALTxCommon02_UpdateStatusSyncThirdPartyV2', 
                svApiFmt.InVal          = [datetime, result.data[i].c1 ,'2',JSON.stringify(rsSend.data)];
                svApiFmt.TotInVal       = 4
              }
              const rsUpt = await this.server.callSocketHos(svApiFmt);
              this.logger.logInfo('-------------ALTxCommon02_UpdateStatusSyncThirdPartyV2 < '+ JSON.stringify(rsUpt)+' >----------------\n\n')
            }
          }
        }
        this.logger.logInfo('------------- END < GetListSyncThirdparty >----------------')
      }
    } catch (error) {
      this.logger.logError('------------- ERROR END < ' + JSON.stringify(error) + '>----------------')
    }
    return {
      success: true,
      message: "Thực hiện thành công"
    }
  }

  async axiosSendPartner (data:any):Promise<AxiosResponse>{
    try {
      const config: AxiosRequestConfig ={
        method: 'post',
        baseURL: this.configService.get('PS_HOSTNAME') ?? 'http://************:8100/',
        url: '/tsol/apikey/tuxsvc/dr/account/dr-sync-account-infor',
        headers:{
          'Content-Type': 'application/json', 
          'apiKey': this.configService.get('PS_API_KEY')
        },
        data: JSON.stringify(data)
      }
      this.logger.logInfo('axiosSendPartner config: ' + JSON.stringify(config));
      const send2Patner = await axios.request(config)
      // const client = axios.create(config)
      // axiosRetry(client, { retries: 3 });
      // const send2Patner = await client.get('')
      this.logger.logInfo(`axiosSendPartner: ${send2Patner.status} `, JSON.stringify(send2Patner.data));
      return send2Patner
    } catch (error) {
      console.log('error: ', error);
      this.logger.logInfo('------------- ERROR axiosSendPartner < ' + JSON.stringify(error) + '>----------------')
      return error
    }
  }
  async axiosSendChangePW (data:any):Promise<AxiosResponse>{
    try {
      const config: AxiosRequestConfig ={
        method: 'post',
        baseURL: this.configService.get('PS_HOSTNAME') ?? 'http://************:8100/',
        url: '/tsol/apikey/tuxsvc/dr/account/change-password',
        headers:{
          'Content-Type': 'application/json', 
          'apiKey': this.configService.get('PS_API_KEY')
        },
        data: JSON.stringify(data)
      }
      this.logger.logInfo('axiosSendChangePW config: ' + JSON.stringify(config));
      const send2Patner = await axios.request(config)
      this.logger.logInfo(`axiosSendChangePW: ${send2Patner.status} `, JSON.stringify(send2Patner.data));
      return send2Patner
    } catch (error) {
      console.log('error: ', error);
      this.logger.logInfo('------------- ERROR axiosSendChangePW < ' + JSON.stringify(error) + '>----------------')
      return error
    }
  }
}
