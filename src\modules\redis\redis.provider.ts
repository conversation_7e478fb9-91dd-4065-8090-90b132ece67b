import {Provider} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import Redis from 'ioredis'

export type RedisClient = Redis 

export const redisProvider : Provider = {
    useFactory:(config: ConfigService):RedisClient =>{
        return new Redis({
            host: config.get('REDIS_HOST'),
            port: config.get('REDIS_PORT'),
        })
    },
    inject: [ConfigService],
    provide: 'REDIS_CLIENT' 
}