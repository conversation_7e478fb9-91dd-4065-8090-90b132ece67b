import { Inject, Injectable } from '@nestjs/common';
import { RedisClient } from './redis.provider';

@Injectable()
export class RedisService {
  public constructor(
    @Inject('REDIS_CLIENT') 
    private readonly client: RedisClient 
  ){}

  async set (key: string, value: string, expirationSeconds?: number){
    await this.client.set(key,value)
  }

  async get (key: string):Promise<string |null >{
    return await this.client.get(key)
  }  
  async del (key: string):Promise<number >{
    return await this.client.del(key)
  }
}
