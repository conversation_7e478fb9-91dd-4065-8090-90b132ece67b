import { <PERSON>, Get, Logger, Query, Res } from '@nestjs/common';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}
  private readonly logger = new Logger(AppController.name);

  // @Get()
  // display(@Res() res, @Query('filename') filename: string) {
  //   res.sendFile(filename, { root: './uploads' });
  // }
}
