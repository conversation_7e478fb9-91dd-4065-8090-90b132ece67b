import { Module } from "@nestjs/common";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { ConfigModule } from "@nestjs/config";
import { configuration } from "./config";
import { ScheduleModule } from "@nestjs/schedule";
import * as modules from "./modules";
@Module({
  imports: [
    ...Object.values(modules),
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
      envFilePath: [".env", ".env.local"],
    }),
    ScheduleModule.forRoot(),
    // TypeOrmModule.forRootAsync({
    //   useClass: DatabaseDEV,
    //   inject: [ConfigService],
    // }),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
