/* --------------------------------------------------------
 * Author <PERSON><PERSON>
 * Email <EMAIL>
 * Phone 0906.918.738
 * Created: 2023-11-04
 * Change Log: Time: User Change | Content
 * 2023-11-04: NhacVB | Setup Init Source
 *------------------------------------------------------- */

import type { config as integrate } from './env/default';
import type { config as develop } from './env/development';
import type { config as production } from './env/production';

export type ObjecType = Record<string, unknown>;
export type DEV = typeof integrate;
export type UAT = typeof develop;
export type PROD = typeof production;
export type Config = DEV & UAT & PROD;
