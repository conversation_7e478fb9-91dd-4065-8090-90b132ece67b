module.exports ={
  apps : [
  {
    name: 'API-GW-INFOBIT',
    script: 'dist/main.js',
    args: 'one two',
    cron_restart: '0 0 * * *',
    log_date_format: 'YYYY-MM-DD HH:mm',
    autorestart: true,
    max_memory_restart: '4G',
    watch: true,
    env: {
        PORT: 9696,
        // # DATABASE 1
        // DB1_HOST:'**************',
        // DB1_NAME:'MYSQL_CONNECTION',
        // DB1_TYPE:'mysql', 
        // DB1_PORT:'3306',
        // DB1_USERNAME:'root',
        // DB1_PASSWORD:'a2j3b2ui38iqweJFHSJ1qw312f',
        // DB1_SCHEME:'miwra',
      
        // # SERVICES EMAIL
        // EMAIL_AUTH_USER:'<EMAIL>',
        // EMAIL_AUTH_PASS:'ooxsdclipugkhsjk',
        // EMAIL_HOST:'smtp.gmail.com',
        // EMAIL_PORT:587,
        // EMAIL_TSL_CIPHERS:'SSLv3',

          // # SERVER FTP
        // FTP_HOST:'localhost',
        // FTP_USER:'root',
        // FTP_PASS:'Shinhan@123',
        // FTP_PORT:'21',
    },
  },
],
};
