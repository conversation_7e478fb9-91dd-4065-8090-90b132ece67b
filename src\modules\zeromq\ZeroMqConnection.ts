import { Listener } from './ZeroMqListener';
import * as zmq from 'zeromq/v5-compat';
import * as url from 'url';
import { SSVMwInfo } from './dto/SsvMwInfo.dto';
// import {
//   ServerRequestApi,
//   ServerResponseApi,
// } from '../api-infobip/dto/ServerAPI.dto';
import { Logger } from '@nestjs/common';

const _ = require('lodash');

import { CheckJsonValid } from '../../utils/global.service';
import { ServerRequestApi, ServerResponseApi } from '../open-api/dto';

// Số 3
export class ZeroMq4Connection extends Listener {
  // Xuất Logger
  private logger: any | null = null;
  // Subcribe TOPIC from tcp://***********:2706
  private subscriber = zmq.socket('sub');
  private subUrl: string | null = null;

  //Request  from tcp://***********:6868
  private requestSocket = zmq.socket('dealer');
  private requestSocketHos = zmq.socket('dealer');

  private reqUrl: string | null = null;
  private reqHosUrl: string | null = null;

  constructor({
    reqUrl,
    reqHosUrl,
    pullUrl,
  }: {
    reqUrl: string;
    reqHosUrl: string;
    pullUrl: string;
  }) {
    super();
    this.reqUrl = reqUrl;
    this.subUrl = pullUrl;
    this.reqHosUrl = reqHosUrl;
    this.logger = new Logger('ZeroMq4Connection');
    this.requestSocket = zmq.socket('dealer');
    this.requestSocketHos = zmq.socket('dealer');
    this.requestSocket.connect(reqUrl);
    this.requestSocketHos.connect(reqHosUrl);
  }

  // Khởi tạo subcribe Topic từ tcp://***********:2706
  public connect() {
    const { subUrl } = this;
    this.logger = new Logger('ZeroMq4Connection');
    if (!subUrl || !url.parse(subUrl).hostname) {
      throw new Error('subUrl invalid.');
    }
    /**
     * Subscribe Event Publish from tcp://***********:2706
     * arraySub = [] : danh sách topic theo dõi
     */
    this.subscriber.subscribe('LOGIN_CONNECT');
    // check connect host
    // this.PingService(this.subscriber);
    this.subscriber.monitor(3000, 1).connect(subUrl);
    // handle messages from publisher
    this.subscriber.on(
      'message',
      function (
        empty,
        SocketTransId,
        seq,
        index,
        data,
        msgcode,
        msgtxt,
        result,
      ) {
        if (data) {
          this.logger.log('Subscriber Got a message: ' + JSON.parse(data));
        }
      },
    );
    // connect to publisher
    this.subscriber.connect(subUrl);

    // Ping hệ thống FOS
    const SSVmwInfoFOS = new SSVMwInfo();
    SSVmwInfoFOS.SocketId =
      'ALtMW.NodejS:' + Math.floor(Math.random() * 10000000);
    SSVmwInfoFOS.SSVMWHost = this.reqUrl;
    SSVmwInfoFOS.Socket = this.requestSocket;
    SSVmwInfoFOS.IsConnect = false;
    SSVmwInfoFOS.TotalReq = SSVmwInfoFOS.TotalReq++ || 1;
    SSVmwInfoFOS.Socket.on(
      'message',
      function (
        empty,
        SocketTransId,
        seq,
        index,
        data,
        msgcode,
        msgtxt,
        result,
      ) {
        try {
          const logger = new Logger('ZeroMq4Connection');
          if (seq.toString() == 'PING-PONG') {
            logger.log(
              'PingService FOS: ' +
              JSON.stringify({
                msgtxt: msgtxt.toString(),
                result: result.toString(),
              }),
            );
          }
        } catch (error) {
          console.log('error: ', error);
        }
      },
    );

    // Ping hệ thống HOS
    const SSVmwInfoHOS = new SSVMwInfo();
    SSVmwInfoHOS.SocketId =
      'ALtMW.NodejS:' + Math.floor(Math.random() * 10000000);
    SSVmwInfoHOS.SSVMWHost = this.reqHosUrl;
    SSVmwInfoHOS.Socket = this.requestSocketHos;
    SSVmwInfoHOS.IsConnect = false;
    SSVmwInfoHOS.TotalReq = SSVmwInfoHOS.TotalReq++ || 1;
    SSVmwInfoHOS.Socket.once(
      'message',
      function (
        empty,
        SocketTransId,
        seq,
        index,
        data,
        msgcode,
        msgtxt,
        result,
      ) {
        try {
          const logger = new Logger('ZeroMq4Connection');
          if (seq.toString() == 'PING-PONG') {
            logger.log(
              'PingService HOS: ' +
              JSON.stringify({
                msgtxt: msgtxt.toString(),
                result: result.toString(),
              }),
            );
          }
        } catch (error) {
          console.log('error: ', error);
        }
      },
    );

    // setInterval(() => {
    //   this.PingServiceFOS(SSVmwInfoFOS);
    //   this.PingServiceHOS(SSVmwInfoHOS);
    // }, 15000);
  }



  // kiểm ta tín hiệu mw còn kết nối hay không
  private PingServiceFOS(SSVmwInfo: SSVMwInfo) {
    if (SSVmwInfo.Socket == undefined) return;
    SSVmwInfo.Socket.send([
      '',
      SSVmwInfo.SocketId,
      'PING-PONG',
      '0',
      '0',
      'NodeJs',
      'PING',
    ]);
  }

  // gửi tín hiệu FOS
  public send2FOS(SSVmwInfo: SSVMwInfo, data: ServerRequestApi) {
    SSVmwInfo.TotalReq++;
    SSVmwInfo.Socket.send([
      '',
      SSVmwInfo.SocketId,
      data.WorkerName,
      data.ClientSeq,
      data.TimeOut,
      JSON.stringify(data),
      data.ServiceName,
    ]);
  }

  /** KẾT NỐI HOS: 10.22.22.10:5555 */
  // kiểm ta tín hiệu mw còn kết nối hay không
  private PingServiceHOS(SSVmwInfo: SSVMwInfo) {
    if (SSVmwInfo.Socket == undefined) return;
    SSVmwInfo.Socket.send([
      '',
      SSVmwInfo.SocketId,
      'PING-PONG',
      '0',
      '0',
      'NodeJs',
      'PING',
    ]);
  }

  // Call server FOS BE 0
  public async SSVMWCreateConnection(
    input: ServerRequestApi,
  ): Promise<ServerResponseApi> {
    const connect = zmq.socket('dealer');
    const SSVmwInfo = new SSVMwInfo();
    SSVmwInfo.Socket = connect.connect(this.reqUrl);
    const SocketID = 'ALtMW.NodejS:' + Math.floor(Math.random() * 10000000);
    SSVmwInfo.SSVMWHost = this.reqUrl;
    SSVmwInfo.IsConnect = false;
    SSVmwInfo.TotalReq = 0;
    SSVmwInfo.SocketId = SocketID;

    this.send2FOS(SSVmwInfo, input);
    let param = new ServerResponseApi();
    param.data = [];

    return await new Promise((resolve) => {
      SSVmwInfo.Socket.on(
        'message',
        function (
          empty,
          SocketTransId,
          seq,
          index,
          data,
          msgcode,
          msgtxt,
          result,
        ) {
          //const param = new ServerResponseApi();

          param.message = msgtxt.toString();
          param.success = result.toString() == 1 ? true : false;
          param.msgCd = msgcode.toString();

          console.log(
            '+++++>[' +
            SocketID +
            ']  SERVICE [' +
            input.ServiceName +
            '] >>****>> ' +
            ' msgcode [' +
            msgcode.toString() +
            '] result [' +
            result.toString() +
            '] msgtxt [' +
            msgtxt.toString() +
            '] BEGIN >>>>>>>>>>',
          );
          //console.log("data ", data.toString());
          console.log(' <<<<<<<<<<<<end');

          //Phải dùng biến global hứng danh sách đưa vào
          let dataReturn = null;
          dataReturn = param.data;
          try {
            //CheckJsonValid call Json parse
            if (data && CheckJsonValid(data.toString())) {
              dataReturn = _.union(dataReturn, JSON.parse(data));
              param.data = dataReturn;
            }
            //Gọi hoàn thành
            if (msgtxt == '[VI000000]: Thực hiện thành công' || msgtxt == '[000000]: Your request already process success !!!') {
              console.log(
                '+++++>[' +
                SocketID +
                '] SERVICE [' +
                input.ServiceName +
                '] >>****>> hoàn thành thoát ^_^!',
              );
              SSVmwInfo.Socket.close();
              param.msgCd = msgcode.toString()
              param.message = msgtxt.toString()
              param.data = dataReturn
              resolve(param);
            }

            //Case lỗi không rơi vào còn data hay hoàn thành

            if (
              msgtxt != 'MORE...' &&
              msgtxt != '[VI000000]: Thực hiện thành công'
              || msgtxt != 'MORE...' && msgtxt == '[000000]: Your request already process success !!!'
            ) {
              param.data = dataReturn
              SSVmwInfo.Socket.close() ;
              resolve(param);
            }
          } catch (error) {
            param.data = dataReturn
            console.log(
              '+++++>[' +
              SocketID +
              '] SERVICE [' +
              input.ServiceName +
              '] >>****>> lỗi thoát ^=_=^! ',
              error,
            );
            SSVmwInfo.Socket.close();
            resolve(param);
          }
        },
      );
    });
  }

  // gửi tín hiệu HOS
  public send2HOS(SSVmwInfo: SSVMwInfo, data: ServerRequestApi) {
    SSVmwInfo.TotalReq++;
    SSVmwInfo.Socket.send([
      '',
      SSVmwInfo.SocketId,
      data.WorkerName,
      data.ClientSeq,
      data.TimeOut,
      JSON.stringify(data),
      data.ServiceName,
    ]);
  }

  // Call server HOS BE
  public async SSVMWHosCreateConnection(
    input: ServerRequestApi,
  ): Promise<ServerResponseApi> {
    const SocketID = 'ALtMW.NodejS:' + Math.floor(Math.random() * 10000000);
    const connectHOS = zmq.socket('dealer');
    const SSVmwInfo = new SSVMwInfo();
    SSVmwInfo.Socket = connectHOS.connect(this.reqHosUrl);

    SSVmwInfo.SocketId = SocketID;
    SSVmwInfo.SSVMWHost = this.reqHosUrl;

    SSVmwInfo.IsConnect = false;
    SSVmwInfo.TotalReq = 0;
    this.send2HOS(SSVmwInfo, input);
    let param = new ServerResponseApi();
    param.data = [];

    return await new Promise((resolve) => {
      SSVmwInfo.Socket.on(
        'message',
        function (
          empty,
          SocketTransId,
          seq,
          index,
          data,
          msgcode,
          msgtxt,
          result,
        ) {
          //const param = new ServerResponseApi();

          param.message = msgtxt.toString();
          param.success = result.toString() == 1 ? true : false;
          param.msgCd = msgcode.toString();

          console.log(
            '+++++>[' +
            SocketID +
            ']  SERVICE [' +
            input.ServiceName +
            '] >>****>> ' +
            ' msgcode [' +
            msgcode.toString() +
            '] result [' +
            result.toString() +
            '] msgtxt [' +
            msgtxt.toString() +
            '] BEGIN >>>>>>>>>>',
          );
          //console.log("data ", data.toString());
          console.log(' <<<<<<<<<<<<end');

          //Phải dùng biến global hứng danh sách đưa vào
          let dataReturn = null;

          dataReturn = param.data;

          try {
            //CheckJsonValid call Json parse
            if (data && CheckJsonValid(data.toString())) {
              dataReturn = _.union(dataReturn, JSON.parse(data));
              param.data = dataReturn;
            }

            //Gọi hoàn thành
            if (msgtxt == '[VI000000]: Thực hiện thành công' || msgtxt == '[VI080000]: Đăng nhập thành công. Chúc quý khách đầu tư thành công!') {
              console.log(
                '+++++>[' +
                SocketID +
                '] SERVICE [' +
                input.ServiceName +
                '] >>****>> hoàn thành thoát ^_^!',
              );
              SSVmwInfo.Socket.close();
              resolve(param);
            }

            //Case lỗi không rơi vào còn data hay hoàn thành
            if (
              msgtxt != 'MORE...' &&
              msgtxt != '[VI000000]: Thực hiện thành công'
            ) {
              param.data = data.toString();
              SSVmwInfo.Socket.close();
              resolve(param);
            }
          } catch (error) {
            param.data = data.toString();
            console.log(
              '+++++>[' +
              SocketID +
              '] SERVICE [' +
              input.ServiceName +
              '] >>****>> lỗi thoát ^=_=^! ',
              error,
            );
            SSVmwInfo.Socket.close();
            resolve(param);
          }
        },
      );
    });
  }
}
