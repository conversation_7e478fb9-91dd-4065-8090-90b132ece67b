{"id": "en", "words": ["*fakk*", "*fuck*", "*fuk+er", "*fuk+ers", "*fukk*", "*ni+g+er*", "*testical*", "*testicle*", "anal", "anil+ingus*", "anus", "anuses", "<PERSON><PERSON><PERSON>", "ar+se", "ar+sebadger", "ar+sehead*", "ar+sehol*", "ashol*", "ass+", "ass+hol", "ass+hole*", "ass+wipe", "aswipe", "az+hol", "az+hole*", "b_d_s_m", "ball+bag*", "ball+sack*", "bastard*", "bbc", "bellend", "biatch*", "bitch*", "blow_job*", "boff+in*", "bol+ock+*", "bon+er", "boo+b", "boo+bs", "boo+bz", "btch", "bugg+er*", "bul+shit*", "butt+", "butt+hol", "butt+hole*", "butt+much*", "butt+plug*", "carpet_muncher*", "cawk", "cawks", "clit+", "clit+er*", "clit+or*", "clit+s", "clit+z", "cock", "cock_sucker*", "cock_suckin*", "cockface*", "cockhead*", "cockmunch*", "cocknose*", "cocks", "cocks_sucker*", "cockwomble", "cockz", "crap", "cum", "cum+er", "cum+ing", "cumbub+le*", "cumdumpst*", "cumshot*", "<PERSON><PERSON><PERSON>", "cun+il+ingus*", "cunt+", "cuntlick*", "cuntpud+le", "damn*", "dick", "dickhead*", "dicks", "dickwe+d*", "dickweasel", "dike", "dikes", "dil+do*", "dog+ie_styl*", "dog+y_styl*", "douche_bag", "douche_bags", "dupa", "dyke", "dykes", "ejackulate", "erotic*", "escort*", "fackin*", "fag", "fagat", "faget", "fagg+ot*", "fagget", "faggit", "faggt", "fagt", "fan+yflap*", "fatass*", "feasting", "fecal", "fingerfack*", "foreskin*", "fuk", "fuk+ah", "fuk+in*", "fy", "gang+_b+ang*", "get_laid", "gobshit+*", "god+am", "god+amn*", "godsd+am", "godsd+amn*", "gook", "got_laid", "hard_on", "hoer", "hooker*", "hore", "horny", "horsesh+it+", "idiot*", "j_o_i", "jack_as+", "jack_off", "jack_sh+it+", "jap", "japs", "jerk_off", "jiz+_stain", "jiz+breath", "jiz+cock", "joi", "kike", "kinky", "klit", "knob", "knobbing", "knobhead*", "kunt", "kunts", "kuntz", "ladyboy*", "<PERSON><PERSON>", "masturbat*", "milf", "nazi", "nazis*", "naziz", "negr", "neo_nazi+", "ni+g+r", "ni+g+rs", "ni+gg+", "nimphoman*", "nip+le", "nip+les", "nutsack*", "nutsuck*", "orgas+m", "orgas+mo", "paedofil*", "paedophil*", "pedofil*", "pedophil*", "penis*", "phuck*", "pimp", "pimpis", "pimps", "pimpz", "pis+flap*", "pis+ing", "pis+wizard", "piss+", "porn", "porno", "prick+", "puss+e", "puss+y", "quim", "rape", "raping", "rectum", "redneck*", "s.o.b", "sadism*", "semen", "sex", "sexy", "sh+-it+", "sh+it+", "sh+it+e", "sh+it+y", "sh+it_hol*", "shemale*", "shit+bag*", "shit+house", "shit+magnet", "shit+pouch", "skanck", "skank", "slut+", "slut+y", "strap_on", "suicid*", "svastik*", "swastik*", "swinger", "threesome", "thundercunt*", "tit+", "tit+ie", "tit+ies", "tit+s", "tit+ywank", "todger", "turd", "twat", "twat+waf+l*", "twathead*", "twatty*", "twunt", "twunter", "vagina*", "viagra*", "vibrator", "voyeur*", "voyuer*", "vul+va*", "wank", "wanker*", "wankface", "wetback*", "whoar", "whore", "wiseass+", "wtf", "xrated", "zo+phil+i*", "javascript", "expression", "eval", "&lt;", "onmousew<PERSON><PERSON>", "&gt;", "innerHTML", "onactive", "onfocusout", "charset", "ondataavailable", "oncut", "applet", "meta", "document", "onafteripudate", "onclick", "onkeyup", "onkeypress", "string", "onmousedown", "onchange", "onload", "xml", "create", "onbeforeactivate", "onbeforecut", "onbounce", "blink", "link", "append", "binding", "onbeforecopy", "onbeforedeactivate", "ondbclick", "onmouseenter", "ondeactivate", "onmouseout", "style", "alert", "ondatasetchaged", "ondrag", "on<PERSON><PERSON>ver", "script", "embed", "msgbox", "cnbeforeprint", "ondragend", "onsubmit", "refresh", "cnbeforepaste", "ondragenter", "onmouseend", "object", "void", "onbeforeeditfocus", "ondragleave", "onresizestart", "iframe", "cookie", "onbeforeuload", "ondrago<PERSON>", "onuload", "frame", "<PERSON><PERSON><PERSON>", "onbeforeupdate", "ondragstart", "onselectstart", "frameset", "onpaste", "onpropertychange", "ondrop", "onreset", "ilayer", "onresize", "ondatasetcomplete", "onerror", "onmove", "layer", "bgsound", "onselect", "oncellchange", "onfinish", "onstop", "base", "onlayoutcomplete", "onfocus", "onrowexit", "title", "onbefore", "onmouseup", "onrowenter", "oncontextmenu", "onblur", "onselectionchange", "vbscript", "onerrorupdate", "onstart", "onfocusin", "onhelp", "onrowsinserted", "oncontrolselected", "onkeydown", "onfilterchage", "onrowsdelete", "onlosecapture", "onreadystatechange", "onmouseleave", "<PERSON><PERSON><PERSON><PERSON>"], "lookalike": {"$": "s", "@": "a", "<": "c", "!": "i", "+": "t", "0": "o", "1": "i", "2": "z", "3": "z", "5": "s", "8": "b"}, "phrase": []}