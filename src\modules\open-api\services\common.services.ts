import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { randomUUID } from "crypto";
import { ServerRequestApi } from "../dto";
import { ServerAPIService } from "./serverAPI.service";
import { GetIPAddress } from "src/utils";

// const LIST_SEV_ORD =['FOSxBuyOrder','FOSxSellOrder','FOSxVipOrder01','FOSxModifyOrder', 'FOSxCancelOrder']
const LIST_SEV_ORD =['ALTxBuyOrder','ALTxSellOrder','ALTxChangeOrder', 'ALTxCancelOrder']
@Injectable()
export class OpenAPIService {
  constructor(
    private configService: ConfigService,
    private server: ServerAPIService,
  ) {}
  private readonly logger = new Logger(OpenAPIService.name);
  // API Public test connection
  async PostMofin(data: ServerRequestApi): Promise<any> {
    let svApiFmt = new ServerRequestApi();
    svApiFmt.ClientSeq = new Date().getTime();
    svApiFmt.Lang           = data.Lang || 'VI', 
    svApiFmt.WorkerName     = data.WorkerName,           //'FOSqMkt02';
    svApiFmt.Operation      = data.Operation,            //'Q';
    svApiFmt.ServiceName    = data.ServiceName,         //'FOSqMkt02_IndexMgt';
    svApiFmt.InVal          = data.InVal;
    svApiFmt.AcntNo         = data.AcntNo;        
    svApiFmt.TotInVal       = data.TotInVal;
    svApiFmt.IPPrivate      = GetIPAddress();
    svApiFmt.IPPublic       = data.IPPublic ? data.IPPublic : GetIPAddress();
    this.logger.log(JSON.stringify(svApiFmt));

    // Thêm thông số không cần thiết nhưng phải khai báo
    const result = await this.server.callSocketHos(svApiFmt);
    this.logger.log('-------------result < '+ JSON.stringify(result)+' >----------------')
    try {
      return {
        ...result,
        data: JSON.parse(result.data)
      }
    } catch (error) {
      return result
    }
  }

  //API BOS
  async PostBos(data: ServerRequestApi): Promise<any> {
    let svApiFmt = new ServerRequestApi();
    svApiFmt.ClientSeq = new Date().getTime();
    svApiFmt.Lang           = data.Lang || 'VI';
    svApiFmt.WorkerName     = data.WorkerName;        
    svApiFmt.AcntNo         = data.AcntNo;        
    svApiFmt.Operation      = data.Operation;         
    svApiFmt.ServiceName    = data.ServiceName;        
    svApiFmt.InVal          = data.InVal;
    svApiFmt.TotInVal       = data.TotInVal;
    svApiFmt.IPPrivate      = GetIPAddress();
    svApiFmt.IPPublic       = data.IPPublic ? data.IPPublic : GetIPAddress();

    // worker order thì gọi API
    if(LIST_SEV_ORD.includes(data.WorkerName)){
      // gọi API đăng nhập Tài khoản Khách hàng 081C046836
      const accountLogin =  this.configService.get('MOFIN_ACCOUNT_BOS') || "huy.tn";
      this.logger.log('----> accountLogin: '+ JSON.stringify(accountLogin));
      const login:any = await this.loginBos(accountLogin) ;
      const { success }:any = login;
      this.logger.log('----> login: '+ JSON.stringify(login.data[0]));
      if(!success){
        return {
          success: false,
          message: "Lỗi thông không đăng nhập thành công!",
          data:[]
        }
      }
      svApiFmt.Lang           = 'EN';
      svApiFmt.MdmTp          = this.configService.get('MOFIN_CHANNEL_MDMTP') || "00";
      svApiFmt.MWLoginID      = this.configService.get('MOFIN_CHANNEL_ID')    || "BOS";
      svApiFmt.MWLoginPswd    = this.configService.get('MOFIN_CHANNEL_PASS')  || "1+3+@,E,@+K+(-;/6+G-=+B+L,),I+A,,+J-O//->";
      svApiFmt.Operation      = "I";
      svApiFmt.AppLoginID     = login.data[0].c0;
      svApiFmt.AppLoginPswd   = "2,N,*,5+A,,-5++*4*D";
      svApiFmt.Otp            = login.data[0].c37 || "/,(+/*:*E+3*I";
      // svApiFmt.SessionID      = login.data[0].c0;
      this.logger.log(">>>> ĐẶT LỆNH <<<< " +JSON.stringify(svApiFmt));
    }
    this.logger.log(JSON.stringify(svApiFmt));
    // Thêm thông số không cần thiết nhưng phải khai báo
    const result = await this.server.callSocketHos(svApiFmt);
    if(LIST_SEV_ORD.includes(data.WorkerName)){
      // gọi API check OTP
      if(['ALTxBuyOrder','ALTxSellOrder'].includes(data.WorkerName)){
        data.InVal[0] =this.configService.get('MOFIN_ACCOUNT_USER') || "081ECA9233";
        data.InVal[1] = this.configService.get('MOFIN_ACCOUNT_SUB') || "06";
        data.InVal[2] = this.configService.get('MOFIN_PASS_MASTER') || ",,;,0,;,4,;+>";
        data.InVal[10] = "1";
      } else if(['ALTxChangeOrder'].includes(data.WorkerName)){
        data.InVal[1] = this.configService.get('MOFIN_ACCOUNT_USER') || "081ECA9233";
      } else if(['ALTxCancelOrder'].includes(data.WorkerName)){
        data.InVal[1] = this.configService.get('MOFIN_ACCOUNT_USER') || "081ECA9233";
      }
      this.logger.log('data: ', data);
      // call API Order
      svApiFmt.WorkerName     = data.WorkerName;        
      svApiFmt.Operation      = data.Operation;         
      svApiFmt.ServiceName    = data.ServiceName;        
      svApiFmt.InVal          = data.InVal;
      svApiFmt.TotInVal       = data.TotInVal;
      // svApiFmt.Otp         =  || this.configService.get('MOFIN_OTP') || "/,(+/*:*E+3*I";
      const resultOrder = await this.server.callSocketHos(svApiFmt);
      this.logger.log(">>>> ĐẶT LỆNH THÀNH CÔNG <<<< " +JSON.stringify(resultOrder));
      try {
        return {
          ...resultOrder,
          data: JSON.parse(resultOrder.data )
        }
      } catch (error) {
        this.logger.log(">>>> ĐẶT LỆNH LỖI <<<< " +JSON.stringify(error));
        return resultOrder
      } 
    }
    try {
      return {
        ...result,
        data: JSON.parse(result.data)
      }
    } catch (error) {
      return result
    }
  }

  async loginBos (accNo:string):Promise<any> {
    let svApiFmt = new ServerRequestApi();
    svApiFmt.ClientSeq      = new Date().getTime();
    svApiFmt.MdmTp          = this.configService.get('MOFIN_CHANNEL_MDMTP') || "00";
    svApiFmt.MWLoginID      = this.configService.get('MOFIN_CHANNEL_ID')    || "BOS";
    svApiFmt.MWLoginPswd    = this.configService.get('MOFIN_CHANNEL_PASS')  || "1+3+@,E,@+K+(-;/6+G-=+B+L,),I+A,,+J-O//->";
    svApiFmt.Lang           = 'VI';
    svApiFmt.WorkerName     = 'ALTxCommon';
    svApiFmt.Operation      = 'U';     
    svApiFmt.ServiceName    = 'ALTxCommon_Login';
    // svApiFmt.InVal          = ['login', accNo || '081FIA7414' , this.configService.get('MOFIN_PASS') || '/,D,D+E,*,D+++?,+,.',  '', randomUUID().toString(), 'N', 'MOFIN-ORDER'];
    svApiFmt.InVal          = [accNo, this.configService.get('MOFIN_PASS') || '/,D,D+E,*,D+++?,+,.',  '', randomUUID().toString()+ '-MOFIN-ORDER'];
    svApiFmt.TotInVal       =  4;
    svApiFmt.IPPrivate      = GetIPAddress();
    svApiFmt.IPPublic       = GetIPAddress();
    this.logger.log(">>>> ĐĂNG NHẬP <<<< " +JSON.stringify(svApiFmt));
    // 0000060284|06|01|20240820171120|3784031|KFDIXRKE
    const result = await this.server.callSocketHos(svApiFmt);
    this.logger.log('->>>>>>> result: ', result);
    try {
      return {
        ...result,
        data: JSON.parse(result.data)
      }
    } catch (error) {
      return result
    }
  }

  //API FOS
  async PostFos(data: ServerRequestApi): Promise<any> {
    let svApiFmt = new ServerRequestApi();
    svApiFmt.ClientSeq = new Date().getTime();
    svApiFmt.Lang           = data.Lang || 'VI';
    svApiFmt.WorkerName     = data.WorkerName;        
    svApiFmt.AcntNo         = data.AcntNo;        
    svApiFmt.Operation      = data.Operation;         
    svApiFmt.ServiceName    = data.ServiceName;        
    svApiFmt.InVal          = data.InVal;
    svApiFmt.TotInVal       = data.TotInVal;
    svApiFmt.IPPrivate      = GetIPAddress();
    svApiFmt.IPPublic       = data.IPPublic ? data.IPPublic : GetIPAddress();

    // worker order thì gọi API
    if(LIST_SEV_ORD.includes(data.WorkerName)){
      // gọi API đăng nhập Tài khoản Khách hàng 081C046836
      const login:any = await this.loginFos(data.AcntNo) ;
      console.log('login: ', login);
      const { success }:any = login;
      if(!success){
        return {
          success: false,
          message: "Lỗi không đăng nhập thành công!",
          data:[]
        }
      }
      svApiFmt.Lang           = 'EN';
      svApiFmt.MdmTp          = this.configService.get('MOFIN_CHANNEL_MDMTP') || "02";
      svApiFmt.MWLoginID      = this.configService.get('MOFIN_CHANNEL_ID')    || "WEB";
      svApiFmt.MWLoginPswd    = this.configService.get('MOFIN_CHANNEL_PASS')  || ",+A,3-)-C.*,6,9,=+F*K.N*M.=+)+J,004";
      svApiFmt.Operation      = "I";
      svApiFmt.AppLoginID     = login.data[0].c1;
      svApiFmt.AppLoginPswd   = this.configService.get('MOFIN_PASS') || "2,N,*,5+A,,-5++*4*D";
      svApiFmt.Otp            = login.data[0].c37 || "/,(+/*:*E+3*I";
      svApiFmt.SessionID      = login.data[0].c0;
      this.logger.log(">>>> ĐẶT LỆNH <<<< " +JSON.stringify(svApiFmt));
    }
    this.logger.log(JSON.stringify(svApiFmt));
    // Thêm thông số không cần thiết nhưng phải khai báo
    const result = await this.server.callSocketFos(svApiFmt);

    // Xác thực OTP -> gọi lệnh order
    if(result.msgCd == '010038'){
      if(LIST_SEV_ORD.includes(data.WorkerName)){
        // gọi API check OTP
        svApiFmt.MdmTp          = this.configService.get('MOFIN_CHANNEL_MDMTP') || "02";
        svApiFmt.MWLoginID      = this.configService.get('MOFIN_CHANNEL_ID')    || "WEB";
        svApiFmt.MWLoginPswd    = this.configService.get('MOFIN_CHANNEL_PASS')  || ",+A,3-)-C.*,6,9,=+F*K.N*M.=+)+J,004";
        svApiFmt.WorkerName     = 'FOSxID01';        
        svApiFmt.Operation      = 'I';         
        svApiFmt.ServiceName    = 'FOSxID01_OTPManagement';        
        svApiFmt.InVal          = ['check_otp', svApiFmt.Otp, 'default'];
        svApiFmt.TotInVal       = 3;
        this.logger.log(">>>> XÁC THỰC OTP <<<< " +JSON.stringify(svApiFmt));
        const resultOTP = await this.server.callSocketFos(svApiFmt);
        console.log('resultOTP: ', resultOTP);
        if(resultOTP.msgCd== '000000' && resultOTP.success){
          // call API Order
          svApiFmt.WorkerName     = data.WorkerName;        
          svApiFmt.Operation      = data.Operation;         
          svApiFmt.ServiceName    = data.ServiceName;        
          svApiFmt.InVal          = data.InVal;
          svApiFmt.TotInVal       = data.TotInVal;
          // svApiFmt.Otp         =  || this.configService.get('MOFIN_OTP') || "/,(+/*:*E+3*I";
          const resultOrder = await this.server.callSocketFos(svApiFmt);
          this.logger.log(">>>> ĐẶT LỆNH THÀNH CÔNG <<<< " +JSON.stringify(resultOrder));
          try {
            return {
              ...resultOrder,
              data: JSON.parse(resultOrder.data )
            }
          } catch (error) {
            this.logger.log(">>>> ĐẶT LỆNH LỖI <<<< " +JSON.stringify(error));
            return resultOrder
          } 
        }
      }
    }
    try {
      return {
        ...result,
        data: JSON.parse(result.data)
      }
    } catch (error) {
      return result
    }
  }

  async loginFos(accNo:string):Promise<any> {
    let svApiFmt = new ServerRequestApi();
    svApiFmt.ClientSeq      = new Date().getTime();
    svApiFmt.MdmTp          = this.configService.get('MOFIN_CHANNEL_MDMTP') || "02";
    svApiFmt.MWLoginID      = this.configService.get('MOFIN_CHANNEL_ID')    || "WEB";
    svApiFmt.MWLoginPswd    = this.configService.get('MOFIN_CHANNEL_PASS')  || ",+A,3-)-C.*,6,9,=+F*K.N*M.=+)+J,004";
    svApiFmt.Lang           = 'VI';
    svApiFmt.WorkerName     = 'FOSxID02';
    svApiFmt.Operation      = 'U';     
    svApiFmt.ServiceName    = 'FOSxID02_Login';
    svApiFmt.InVal          = ['login', accNo || '081FIA7414' , this.configService.get('MOFIN_PASS') || '/,D,D+E,*,D+++?,+,.',  '', randomUUID().toString(), 'N', 'MOFIN-ORDER'];
    svApiFmt.TotInVal       =  7;
    svApiFmt.IPPrivate      = GetIPAddress();
    svApiFmt.IPPublic       = GetIPAddress();
    this.logger.log(">>>> ĐĂNG NHẬP <<<< " +JSON.stringify(svApiFmt));
    // 0000060284|06|01|20240820171120|3784031|KFDIXRKE
    const result = await this.server.callSocketFos(svApiFmt);
    try {
      return {
        ...result,
        data: JSON.parse(result.data)
      }
    } catch (error) {
      return result
    }
  }
}
