export const config = {
  env_node: process.env.ENV_NODE || 'development',
  db_uat: {
    host: process.env.DB1_HOST,
    name: process.env.DB1_NAME,
    type: process.env.DB1_TYPE,
    port: process.env.DB1_PORT,
    username: process.env.DB1_USERNAME,
    password: process.env.DB1_PASSWORD,
    scheme: process.env.DB1_SCHEME,
  },
  ftp_server: {
    user: process.env.FTP_USER,
    pass: process.env.FTP_PASS,
    host: process.env.FTP_HOST,
    port: process.env.FTP_PORT,
  },
  mail_server: {
    user: process.env.EMAIL_AUTH_USER,
    pass: process.env.EMAIL_AUTH_PASS,
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    tsl: process.env.EMAIL_TSL_CIPHERS,
  },
  ZeroMqSocket: {
    apiKey: process.env.API_KEY || 'CHANGEME',
    reqUrl: process.env.REQ_URL || 'tcp://***********:6868',
    pullUrl: process.env.PULL_URL || 'tcp://***********:2706',
    reqHosUrl: process.env.REQ_URL_HOS || 'tcp://***********:5555',
  },
};
