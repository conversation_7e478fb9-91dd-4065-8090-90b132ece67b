import { MailerOptions, MailerOptionsFactory } from '@nestjs-modules/mailer';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class MailerConfig implements MailerOptionsFactory {
  constructor(private configService: ConfigService) {}

  createMailerOptions(): MailerOptions | Promise<MailerOptions> {
    const { internalConfig }: any = this.configService;
    return {
      transport: {
        service: 'Gmail',
        host: internalConfig.mail_server.host,
        port: Number(internalConfig.mail_server.port),
        tls: {
          ciphers: internalConfig.mail_server.tsl,
        },
        secure: false, // true for 465, false for other ports
        auth: {
          user: internalConfig.mail_server.user,
          pass: internalConfig.mail_server.pass,
        },
      },
    };
  }
}
