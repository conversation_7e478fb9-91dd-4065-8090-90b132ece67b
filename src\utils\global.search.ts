// const { Index } = require('flexsearch');
// const chalk = require('chalk');

// export class GlobalSearch {
//   static globalAccArray: Array<any>;
//   static searchAccGlobal: any;

//   static async indexAcc(): Promise<void> {
//     let indexAcc = await this.indexData(this.globalAccArray);
//     this.searchAccGlobal = indexAcc;

//     return indexAcc;
//   }

//   static async searchAcc(query: string): Promise<void> {
//     console.log(chalk.cyan('<<*Start search data  begin*>>'));
//     let resultSearch = this.searchData(this.searchAccGlobal, query);
//     console.log('resultSearch ', resultSearch);
//     console.log(chalk.cyan('<<*Start search data  end*>>'));

//     return resultSearch;
//   }

//   static searchData(index: any, query: string): Promise<void> {
//     return index.search(query);
//   }

//   static indexData(arrayIndex: any): Promise<void> {
//     const options = {
//       tokenize: 'strict',
//       cache: false,
//     };
//     const index = new Index(options);

//     console.log(chalk.cyan('<<*Start index data  begin*>>'));

//     if (Array.isArray(arrayIndex)) {
//       arrayIndex.forEach((recipe) => {
//         index.add(recipe.c0, recipe.c0);
//       });
//     }

//     console.log(chalk.cyan('<<*Start index data  end*>>'));

//     return index;
//   }
// }
