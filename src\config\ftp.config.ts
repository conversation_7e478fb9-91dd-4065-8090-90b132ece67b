import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { IConnectionOptions } from 'nestjs-ftp';

@Injectable()
export class FtpConfig {
  constructor(private configService: ConfigService) {}
  createFtpOptions(): IConnectionOptions | Promise<IConnectionOptions> {
    const { internalConfig }: any = this.configService;
    return {
      host: internalConfig?.ftp_server.host,
      user: internalConfig?.ftp_server.user,
      password: internalConfig?.ftp_server.pass,
      port: internalConfig?.ftp_server.port,
      secure: false,
    };
  }
}
