import { ConfigService } from "@nestjs/config";
import { ZeroMqSocket } from "src/modules/zeromq/zeromq.service";

export class GlobalService {
  static globalCheckCron: any;
  static flagSuccessConnect: boolean;
  static listAccArr: any;
  static checkInsertQueue: boolean;
  static checkUpdateQueue: boolean;
  static checkRunSendDailyInfobip : boolean;
  static checkMoveTableInfobip : boolean;
  static setTimeRunJobMove: string ;
}

export const GetIPAddress = () => {
  var interfaces = require('os').networkInterfaces();
  for (var devName in interfaces) {
    var iface = interfaces[devName];
    for (var i = 0; i < iface.length; i++) {
      var alias = iface[i];
      if (
        alias.family === 'IPv4' &&
        alias.address !== '127.0.0.1' &&
        !alias.internal
      )
        return alias.address;
    }
  }
  return '0.0.0.0';
};

export const CheckJsonValid = (inputST: string) => {
  try {
    JSON.parse(inputST);
  } catch (e) {
    return false;
  }
  return true;
};


  // type 1: fos| 2: hos
export const CheckConnection =( type: any, configService: ConfigService, logger:any  ) => {
  try {
    if(!type) return false
    const config = configService.get<ZeroMqSocket>('ZeroMqSocket');  
    let hostname =''
    if(type.toString() === '1'){
      hostname = config.reqUrl
    }else if(type.toString() === '2'){
      hostname = config.reqHosUrl
    }else{
      return false 
    }
    if(hostname.split('//').length <=1){
      return false
    }
    const ip =hostname.split('//')[1].split(':')[0]
    const port =hostname.split('//')[1].split(':')[1]
    let execSync = require('child_process').execSync;
    let cmd = `telnet ${ip} ${port} `;
    let options = {
      encoding: 'utf8'
    };
    logger.log(`----- checkConnection output:${ip}:${port} `);
    const output = execSync(cmd, options)
    return true 
  } catch (error) {
    logger.log(`----- checkConnection output:`, JSON.stringify(error.output));
    const success = `Escape character is '^]'` 
    if( error?.output?.length > 0 &&  error?.output?.filter(ele=> ele?.includes(success)).length > 0 ){
      return true
    }
    return false 
  }
}
