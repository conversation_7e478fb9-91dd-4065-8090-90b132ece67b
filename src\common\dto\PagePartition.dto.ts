import { ApiProperty } from '@nestjs/swagger';

export class PagePartition {
  @ApiProperty({ default: 0, description: 'Tổng số data' })
  totalItem: number;

  @ApiProperty({ default: 0, description: 'Tổng số trang' })
  numPage: number;

  @ApiProperty({ default: 0, description: 'Trang số' })
  currentPage: number;

  @ApiProperty({ default: 0, description: 'Số data trong 1 trang' })
  itemPage: number;
}
