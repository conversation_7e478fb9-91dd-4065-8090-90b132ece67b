/* --------------------------------------------------------
 * Author <PERSON><PERSON>
 * Email <EMAIL>
 * Phone 0906.918.738
 * Created: 2023-11-04
 * Change Log: Time: User Change | Content
 * 2023-11-04: NhacVB | Setup Init Source
 *------------------------------------------------------- */

import type { Config, ObjecType, DEV, UAT, PROD } from './config.interface';

const util = {
  isObject<T>(value: T): value is T & ObjecType {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
  },

  merge<T extends ObjecType, U extends ObjecType>(target: T, source: U): T & U {
    for (const key of Object.keys(source)) {
      const targetValue = target[key];
      const sourceValue = source[key];
      if (this.isObject(targetValue) && this.isObject(sourceValue)) {
        Object.assign(sourceValue, this.merge(targetValue, sourceValue));
      }
    }
    return { ...target, ...source };
  },
};

export const configuration = async (): Promise<Config> => {
  const { config } = await import('./env/default');
  const envName = process.env.NODE_ENV || 'development';
  let { config: environment } = <{ config: PROD }>(
    await import(`./env/${envName}`)
  );
  try {
    const { config: local } = <{ config: any }>(
      await import(`./env/${envName}.local`)
    );
    environment = util.merge(environment, local);
  } catch (ignored) {}
  return util.merge(config, environment);
};
