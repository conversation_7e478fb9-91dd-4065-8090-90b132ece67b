export class ServerResponseApi {
  public success: boolean;
  public message: string;
  public msgCd: string;
  public data: any;
  constructor() {
    this.success = false;
    this.message = 'Failed';
    this.msgCd = '0000';
    this.data = {};
  }
}

export class ServerRequestApi {
  public ClientSeq: number;
  public APILoginID: string;
  public AppLoginID: string;
  public MWLoginPswd: string;
  public MWLoginID: string;
  public APILoginPswd: string;
  public AppLoginPswd: string;
  public Token: string;
  public TradChanel: string;
  public IPPrivate: string;
  public IPPublic: string;
  public Lang: string;
  public CltVersion: string;
  public WorkerName: string;
  public ServiceName: string;
  public TimeOut: number;
  public InVal: any;
  public Operation: string;
  public TotInVal: number;

  public ClientSentTime: string;
  public SecCode: string;
  public AprStat: string;
  public CustMgnBrch: string;
  public CustMgnAgc: string;
  public BrkMgnBrch: string;
  public BrkMgnAgc: string;
  public LoginBrch: string;
  public LoginAgnc: string;
  public AprSeq: string;
  public MakerDt: string;
  public AprIP: string;
  public AprID: string;
  public AprAmt: string;
  public Otp: string;
  public AcntNo: string;
  public SubNo: string;
  public BankCd: string;
  public PCName: string;
  public SessionID: string;
  public MdmTp: string;
  public SID: string;

  constructor() {
    this.ClientSeq = 0;
    this.ClientSentTime = '0';
    this.SecCode = '081';
    this.AprStat = 'N';

    this.AppLoginID = '';
    this.AppLoginPswd = '';

    this.APILoginID = '';
    this.APILoginPswd = '';

    this.MWLoginID = 'BOS';
    this.MWLoginPswd ='6+C+I.5,A-6,L-1,D,(+7.N/=.6*;';
    this.Token = '';
    // this.TradChanel = 'API' // for VNCS test
    this.MdmTp = '00'; // all
    this.AppLoginID = '';
    this.Operation = '';
    this.IPPrivate = '0.0.0.0';
    this.IPPublic = '0.0.0.0';
    this.Lang = 'VI';
    this.CltVersion = '2.0.0';
    this.WorkerName = '';
    this.ServiceName = '';
    this.TimeOut = 90;
    this.InVal = [];
    this.TotInVal = 0;

    //
    this.SID = '01';
    this.CustMgnBrch = '';
    this.CustMgnAgc = '';
    this.BrkMgnBrch = '';
    this.BrkMgnAgc = '';
    this.LoginBrch = '000';
    this.LoginAgnc = '00';
    this.AprSeq = '';
    this.MakerDt = '';
    this.AprIP = '';
    this.AprID = '';
    this.AprAmt = '';
    this.Otp = '';
    this.AcntNo = '';
    this.SubNo = '';
    this.BankCd = '';
    this.PCName = '';
    this.SessionID = '';
  }
}
