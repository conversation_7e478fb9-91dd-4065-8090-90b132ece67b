/* --------------------------------------------------------
 * Author V<PERSON>
 * Email vonha<PERSON>.<EMAIL>
 * Phone 0906.918.738
 * Created: 2023-11-04
 * Change Log: Time: User Change | Content
 * 2023-11-04: NhacVB | Setup Init Source
 *------------------------------------------------------- */
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions, TypeOrmOptionsFactory } from '@nestjs/typeorm';
import { join } from 'path';
// import path from 'path';

@Injectable()
export class DatabaseDEV implements TypeOrmOptionsFactory {
  constructor(private configService: ConfigService) {}

  createTypeOrmOptions(): Promise<TypeOrmModuleOptions> | TypeOrmModuleOptions {
    const { internalConfig }: any = this.configService;
    // console.log('internalConfig: ', internalConfig);
    // console.log('__dirname: ', path.resolve() + '/src/modules');
    return {
      name: internalConfig?.db_dev.name,
      type: internalConfig?.db_dev.type,
      host: internalConfig?.db_dev.host,
      port: internalConfig?.db_dev.port,
      username: internalConfig?.db_dev.username,
      password: internalConfig?.db_dev.password,
      database: internalConfig?.db_dev.scheme,
      // entities: ['dist/modules/**/**/*.entity{.ts,.js}'],
      entities: [join(__dirname, '..', 'modules/**/**/*.entity.{ts,js}')],

      synchronize: true,
      migrationsRun: true,
    };
  }
}

@Injectable()
export class DatabaseUAT implements TypeOrmOptionsFactory {
  constructor(private configService: ConfigService) {}
  createTypeOrmOptions(): Promise<TypeOrmModuleOptions> | TypeOrmModuleOptions {
    const { internalConfig }: any = this.configService;
    return {
      name: internalConfig?.db_uat.name,
      type: internalConfig?.db_uat.type,
      host: internalConfig?.db_uat.host,
      port: internalConfig?.db_uat.port,
      username: internalConfig?.db_uat.username,
      password: internalConfig?.db_uat.password,
      database: internalConfig?.db_uat.scheme,
      entities: ['dist/modules/**/*.entity{.ts,.js}'],
      synchronize: true,
      migrationsRun: true,
    };
  }
}

@Injectable()
export class DatabasePRO implements TypeOrmOptionsFactory {
  constructor(private configService: ConfigService) {}
  createTypeOrmOptions(): Promise<TypeOrmModuleOptions> | TypeOrmModuleOptions {
    const { internalConfig }: any = this.configService;
    return {
      // name: internalConfig?.db_prod.name,
      type: internalConfig?.db_prod.type,
      host: internalConfig?.db_prod.host,
      port: internalConfig?.db_prod.port,
      username: internalConfig?.db_prod.username,
      password: internalConfig?.db_prod.password,
      database: internalConfig?.db_prod.scheme,
      entities: ['dist/modules/**/*.entity{.ts,.js}'],
      synchronize: true,
      migrationsRun: true,
    };
  }
}
