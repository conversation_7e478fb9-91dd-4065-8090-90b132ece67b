import { Body, Controller, Head<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Post, ValidationPipe } from "@nestjs/common";
import { OpenAPIService } from "../services";

@Controller('open-api')
export class OpenAPIController {
    constructor( private readonly appService: OpenAPIService ) { }
    private readonly logger = new Logger(OpenAPIController.name);

    @Post('mofin')
    async fetchPostMofin( @Body(ValidationPipe) data: any, @Headers() header: any, @Ip() ip: any): Promise<any> { 
        // TODO
        this.logger.log('-------------< START MOFIN >----------------')
        this.logger.log('-------------< '+JSON.stringify(data)+' >----------------')
        const host = header['host'].split(':') || [];
        if (host.length > 0) {
            data.IPPublic = host[0] || '0.0.0.0';
        }
        return await this.appService.PostMofin(data)
    }

    @Post('fos')
    async fetchPostFos( @Body(ValidationPipe) data: any, @Headers() header: any, @Ip() ip: any): Promise<any> {
        // TODO
        this.logger.log('-------------< START FOS >----------------')
        this.logger.log('-------------< '+JSON.stringify(data)+' >----------------')
        const host = header['host'].split(':') || [];
        if (host.length > 0) {
            data.IPPublic = host[0] || '0.0.0.0';
        }
        return await this.appService.PostFos(data)
    }

    @Post('mofin/order')
    async fetchPostBosOrder( @Body(ValidationPipe) data: any, @Headers() header: any, @Ip() ip: any): Promise<any> {
        // TODO
        this.logger.log('-------------< START BOS >----------------')
        this.logger.log('-------------< '+JSON.stringify(data)+' >----------------')
        const host = header['host'].split(':') || [];
        if (host.length > 0) {
            data.IPPublic = host[0] || '0.0.0.0';
        }
        return await this.appService.PostBos(data)
    }

    @Post('mofin/fos/order')
    async fetchPostFosOrder( @Body(ValidationPipe) data: any, @Headers() header: any, @Ip() ip: any): Promise<any> {
        // TODO
        this.logger.log('-------------< START FOS >----------------')
        this.logger.log('-------------< '+JSON.stringify(data)+' >----------------')
        const host = header['host'].split(':') || [];
        if (host.length > 0) {
            data.IPPublic = host[0] || '0.0.0.0';
        }
        return await this.appService.PostFos(data)
    }
}

